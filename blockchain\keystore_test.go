package blockchain

import (
	"context"
	"testing"
)

func TestSimpleKeyStore(t *testing.T) {
	keyStore := NewSimpleKeyStorer("")
	err := keyStore.SaveKey(context.Background(), "dummy")
	if err != nil {
		t.<PERSON>rf("while save key: %v", err)
	}
	key, err := keyStore.LoadKey(context.Background())
	if err != nil {
		t.<PERSON>("while load key: %v", err)
	}
	if string(key) != "dummy" {
		t.<PERSON>("error loading the stored key: %v != dummy", string(key))
	}
}
