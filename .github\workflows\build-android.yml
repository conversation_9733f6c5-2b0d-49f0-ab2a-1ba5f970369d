name: Build for Android

on:
  workflow_dispatch:
  release:
    types: [created]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Check out code
      uses: actions/checkout@v2

    - name: Set up Git LFS
      run: |
        git lfs install
        git lfs pull

    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: '1.21.4' # Specify the Go version

    - name: Install and Initialize gomobile
      run: |
        go install golang.org/x/mobile/cmd/gomobile@latest
        gomobile init
        echo "$(go env GOPATH)/bin" >> $GITHUB_PATH

    - name: Install gomobile
      run: go get golang.org/x/mobile/bind@latest

    - name: Create build directory
      run: mkdir -p ./build

    - name: Build mobile package
      run: |
        gomobile bind -v -o ./build/mobile.aar -target=android -androidapi 26 ./mobile
    - name: Set up JDK 11
      uses: actions/setup-java@v2
      with:
        java-version: '11' # Specify the Java version
        distribution: 'adopt'
    - name: Install Android SDK
      run: |
        wget https://dl.google.com/android/repository/commandlinetools-linux-6609375_latest.zip
        unzip commandlinetools-linux-6609375_latest.zip -d $HOME/android-sdk
        mkdir -p $HOME/android-sdk/cmdline-tools/latest
        mv $HOME/android-sdk/cmdline-tools/bin $HOME/android-sdk/cmdline-tools/latest/
        mv $HOME/android-sdk/cmdline-tools/lib $HOME/android-sdk/cmdline-tools/latest/
        echo "ANDROID_HOME=$HOME/android-sdk" >> $GITHUB_ENV
        echo "$HOME/android-sdk/cmdline-tools/latest/bin" >> $GITHUB_PATH
        yes | $HOME/android-sdk/cmdline-tools/latest/bin/sdkmanager --licenses
        $HOME/android-sdk/cmdline-tools/latest/bin/sdkmanager "platforms;android-31" "build-tools;31.0.3"
    - name: Get Release Info
      id: get-release-info
      uses: actions/github-script@v5
      with:
        script: |
          let releaseVersion = '0.0.0';
          try {
            const release = await github.rest.repos.getLatestRelease({
             owner: context.repo.owner,
             repo: context.repo.repo,
            });
            releaseVersion = release.data.tag_name;
          } catch (error) {
            console.log('Error fetching latest release: ', error.message);
          }
          return releaseVersion;
    - name: Print release 
      run: echo "Latest Release is ${{ steps.get-release-info.outputs.result }}"

    - name: Install package with Maven
      run: |
        FILE="-Dfile=./build/mobile.aar"
        mvn install:install-file $FILE -DgroupId=land.fx -DartifactId=fulamobile -Dversion=${{ steps.get-release-info.outputs.result }} -Dpackaging=aar -DgeneratePom=true

    - name: Copy pom.xml and AndroidManifest.xml to build directory
      run: |
        mkdir -p ./build/src/main
        cp ./.github/pom.xml ./build/pom.xml
        cp ./.github/AndroidManifest.xml ./build/src/main/AndroidManifest.xml


    - name: Prepare for Maven deployment
      run: |
        sed -i "s/\${release_version}/${{ steps.get-release-info.outputs.result }}/g" build/pom.xml

    - name: Deploy to GitHub Packages
      working-directory: ./build
      run: |
        mvn deploy -Drelease_version=${{ steps.get-release-info.outputs.result }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

