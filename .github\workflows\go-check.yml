on: [ push, pull_request ]
name: Go Checks

jobs:
  unit:
    runs-on: ubuntu-latest
    name: All
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: recursive
      - uses: actions/setup-go@v4
        with:
          go-version: 1.21.4
      - name: Install staticcheck
        run: go install honnef.co/go/tools/cmd/staticcheck@v0.4.6
      - name: Clear Go module cache
        run: go clean -modcache
      - name: Check that go.mod is tidy
        run: |
          go mod tidy
          if [[ -n $(git ls-files --other --exclude-standard --directory -- go.sum) ]]; then
            echo "go.sum was added by go mod tidy"
            exit 1
          fi
          git diff --exit-code -- go.sum go.mod
      - name: gofmt
        if: success() || failure()
        run: |
          out=$(gofmt -s -l .)
          if [[ -n "$out" ]]; then
            echo $out | awk '{print "::error file=" $0 ",line=0,col=0::File is not gofmt-ed."}'
            exit 1
          fi
      - name: go vet
        if: success() || failure()
        run: go vet ./...
      - name: staticcheck
        if: success() || failure()
        run: |
          set -o pipefail
          staticcheck ./... | sed -e 's@\(.*\)\.go@./\1.go@g'
      - name: go generate
        if: (success() || failure())
        run: |
          git clean -fd # make sure there aren't untracked files / directories
          go generate -x ./...
          # check if go generate modified or added any files
          if ! $(git add . && git diff-index HEAD --exit-code --quiet); then
            echo "go generated caused changes to the repository:"
            git status --short
            exit 1
          fi
