name: Build for iOS

on:
  push:
  pull_request:
  release:
    types: [published]

jobs:
  build:
    name: Build & check fula mobile for ios
    runs-on: macos-latest
    permissions:
      contents: write

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: "1.21.x"
      - name: Go information
        run: |
          go version
          go env

      - name: Build the artifact
        run: make fula-xcframework

      - name: Upload release asset 
        if: github.event_name == 'release'
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }} 
          asset_path: ./build/cocoapods-bundle.zip
          asset_name: cocoapods-bundle.zip
          asset_content_type: application/zip

      - name: Upload release asset checksum
        if: github.event_name == 'release'
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }} 
          asset_path: ./build/cocoapods-bundle.zip.sha256
          asset_name: cocoapods-bundle.zip.sha256
          asset_content_type: text/plain
