name: Go Test
on: [ push, pull_request ]

jobs:
  unit:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: "1.21.x"
      - name: Go information
        run: |
          go version
          go env
      - name: Print GITHUB_WORKSPACE
        run: |
          echo "GITHUB_WORKSPACE: $GITHUB_WORKSPACE"
      - name: Create directory for secrets
        run: |
          mkdir -p $GITHUB_WORKSPACE/secrets
          chmod 777 $GITHUB_WORKSPACE/secrets
      - name: Check permissions
        run: ls -lha $GITHUB_WORKSPACE/secrets
      - name: Run tests
        run: go test -v -shuffle=on ./...
        env:
          SECRETS_DIR: ${{ github.workspace }}/secrets

      - name: Run tests with race detector
        run: go test -v -race ./...
        env:
          SECRETS_DIR: ${{ github.workspace }}/secrets
