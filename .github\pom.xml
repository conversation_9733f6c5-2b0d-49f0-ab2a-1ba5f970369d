<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>land.fx</groupId>
    <artifactId>fulamobile</artifactId>
    <version>${release_version}</version>
    <packaging>aar</packaging>
    <build>
        <plugins>
            <plugin>
                <groupId>com.simpligility.maven.plugins</groupId>
                <artifactId>android-maven-plugin</artifactId>
                <version>4.6.0</version>
                <extensions>true</extensions>
                <dependencies>
                     <dependency>
                         <groupId>javax.xml.bind</groupId>
                         <artifactId>jaxb-api</artifactId>
                         <version>2.3.1</version>
                     </dependency>
                     <dependency>
                         <groupId>org.glassfish.jaxb</groupId>
                         <artifactId>jaxb-runtime</artifactId>
                         <version>2.3.1</version>
                     </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
         <dependencies>
    <!-- Other dependencies -->
    <dependency>
        <groupId>javax.xml.bind</groupId>
        <artifactId>jaxb-api</artifactId>
         <version>2.3.1</version>
         </dependency>
         <dependency>
         <groupId>org.glassfish.jaxb</groupId>
         <artifactId>jaxb-runtime</artifactId>
         <version>2.3.1</version>
         </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>github</id>
            <name>GitHub Packages</name>
            <url>https://maven.pkg.github.com/functionland/go-fula</url>
        </repository>
    </distributionManagement>
</project>
