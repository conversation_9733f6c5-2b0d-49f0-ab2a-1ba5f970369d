name: Build

on: [ push, pull_request ]

jobs:
  build:
    name: Container
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build Container image
        uses: docker/build-push-action@v3
        with:
          context: .
          push: false
          platforms: linux/amd64,linux/arm64